import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    console.log('Testing simple search flow...');

    // First, test if we can get airport Duffel IDs
    const { data: airports, error: airportError } = await supabase
      .from('airports')
      .select('iata_code, duffel_id')
      .in('iata_code', ['MCO', 'LAX'])
      .order('iata_code');

    if (airportError) {
      throw new Error(`Airport lookup failed: ${airportError.message}`);
    }

    console.log('Airport lookup result:', airports);

    // Test a simple job creation (without calling Duffel API)
    const testJobData = {
      search_params: {
        origin: 'MCO',
        destination: 'LAX',
        departureDate: '2025-07-21',
        adults: 1,
        cabinClass: 'economy'
      },
      status: 'pending',
      created_at: new Date().toISOString()
    };

    const { data: job, error: jobError } = await supabase
      .from('duffel_jobs')
      .insert(testJobData)
      .select()
      .single();

    if (jobError) {
      throw new Error(`Job creation failed: ${jobError.message}`);
    }

    console.log('Job created successfully:', job.id);

    return NextResponse.json({
      success: true,
      message: 'Search flow test completed',
      data: {
        airports: airports,
        jobId: job.id,
        searchParams: testJobData.search_params
      }
    });
  } catch (error: any) {
    console.error('Error in search test:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to test search flow',
      },
      { status: 500 }
    );
  }
}
