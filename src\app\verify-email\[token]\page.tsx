'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuthContext } from '@/contexts/AuthContext';

interface VerificationPageProps {
  params: Promise<{
    token: string;
  }>;
}

export default function VerifyEmailPage({ params }: VerificationPageProps) {
  const [status, setStatus] = useState<'verifying' | 'success' | 'error' | 'already-verified'>('verifying');
  const [message, setMessage] = useState('');
  const [userInfo, setUserInfo] = useState<{ firstName?: string; lastName?: string; email?: string } | null>(null);
  const [token, setToken] = useState<string>('');
  const router = useRouter();
  const { user } = useAuthContext();

  useEffect(() => {
    const initializeToken = async () => {
      const resolvedParams = await params;
      setToken(resolvedParams.token);
    };
    initializeToken();
  }, [params]);

  useEffect(() => {
    if (!token) return;

    const verifyEmail = async () => {
      try {
        const response = await fetch(`/api/auth/verify-email/${token}`, {
          method: 'GET',
          credentials: 'include',
        });

        const data = await response.json();

        if (response.ok) {
          if (data.alreadyVerified) {
            setStatus('already-verified');
            setMessage('Your email address has already been verified.');
            // If user is logged in, redirect to home, otherwise to login
            setTimeout(() => {
              if (user) {
                router.push('/');
              } else {
                router.push('/login?verified=true');
              }
            }, 2000);
          } else {
            setStatus('success');
            setMessage('Your email address has been successfully verified!');
            setUserInfo(data.user);

            // If user is logged in, redirect to home, otherwise to login
            setTimeout(() => {
              if (user) {
                router.push('/');
              } else {
                router.push('/login?verified=true');
              }
            }, 3000);
          }
        } else {
          setStatus('error');
          setMessage(data.error || 'Failed to verify email address.');
        }
      } catch (error) {
        console.error('Email verification error:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again.');
      }
    };

    if (token) {
      verifyEmail();
    } else if (token === '') {
      // Still loading token
      return;
    } else {
      setStatus('error');
      setMessage('Invalid verification link.');
    }
  }, [token, router]);

  const handleResendVerification = async () => {
    if (!userInfo?.email) return;

    try {
      const response = await fetch('/api/auth/resend-verification', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email: userInfo.email }),
      });

      if (response.ok) {
        setMessage('A new verification email has been sent to your email address.');
      } else {
        setMessage('Failed to resend verification email. Please try again later.');
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      setMessage('Failed to resend verification email. Please try again later.');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-8 px-2 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-2xl sm:text-3xl font-extrabold text-gray-900">
            Email Verification
          </h2>
        </div>

        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {status === 'verifying' && (
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-base sm:text-sm text-gray-600">
                Verifying your email address...
              </p>
            </div>
          )}

          {status === 'success' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Email Verified Successfully!
              </h3>
              <p className="text-base sm:text-sm text-gray-600 mb-4">
                {message}
              </p>
              {userInfo?.firstName && (
                <p className="text-base sm:text-sm text-gray-600 mb-4">
                  Welcome to GoJumpingJack, {userInfo.firstName}!
                </p>
              )}
              <p className="text-base sm:text-sm text-gray-500">
                {user ? 'You will be redirected to the home page in a few seconds...' : 'You will be redirected to the login page in a few seconds...'}
              </p>
              <div className="mt-4">
                <Link
                  href={user ? "/" : "/login"}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base sm:text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {user ? 'Go to Home' : 'Go to Login'}
                </Link>
              </div>
            </div>
          )}

          {status === 'already-verified' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Already Verified
              </h3>
              <p className="text-base sm:text-sm text-gray-600 mb-4">
                {message}
              </p>
              <div className="mt-4">
                <Link
                  href={user ? "/" : "/login"}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-base sm:text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {user ? 'Go to Home' : 'Go to Login'}
                </Link>
              </div>
            </div>
          )}

          {status === 'error' && (
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Verification Failed
              </h3>
              <p className="text-base sm:text-sm text-gray-600 mb-4">
                {message}
              </p>
              <div className="space-y-3">
                <div>
                  <Link
                    href="/signup"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-base sm:text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Sign Up Again
                  </Link>
                </div>
                <div>
                  <Link
                    href="/login"
                    className="text-base sm:text-sm text-blue-600 hover:text-blue-500 font-medium"
                  >
                    Back to Login
                  </Link>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
