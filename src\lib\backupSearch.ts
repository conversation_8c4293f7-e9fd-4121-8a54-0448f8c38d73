import { FlightSearchParams } from '@/hooks/useDuffelFlightSearch';
import { supabase } from '@/lib/supabaseClient';

interface Airport {
  iata_code: string;
  name: string;
  city_name: string;
}

export interface BackupSearchOptions {
  relaxCabinClass?: boolean;
  expandDates?: boolean;
  expandDateRange?: number; // days to expand on each side
  includeNearbyAirports?: boolean;
}

export interface BackupSearchResult {
  searchParams: FlightSearchParams;
  modifications: string[];
  reason: string;
}

/**
 * Generate backup search parameters when the original search returns no results
 */
export async function generateBackupSearches(
  originalParams: FlightSearchParams,
  options: BackupSearchOptions = {}
): Promise<BackupSearchResult[]> {
  const backupSearches: BackupSearchResult[] = [];

  const {
    relaxCabinClass = true,
    expandDates = true,
    expandDateRange = 3,
    includeNearbyAirports = false,
  } = options;

  // 1. Try with higher cabin class if original was economy
  if (relaxCabinClass && originalParams.cabinClass === 'economy') {
    backupSearches.push({
      searchParams: {
        ...originalParams,
        cabinClass: 'premium_economy',
      },
      modifications: ['Upgraded to Premium Economy'],
      reason: 'More flights available in higher cabin classes',
    });
  }

  // 2. Try with expanded dates (±3 days)
  if (expandDates) {
    const departureDate = new Date(originalParams.departureDate);
    const returnDate = originalParams.returnDate ? new Date(originalParams.returnDate) : null;

    // Try departing 1-3 days earlier
    for (let i = 1; i <= expandDateRange; i++) {
      const newDepartureDate = new Date(departureDate);
      newDepartureDate.setDate(departureDate.getDate() - i);

      const newReturnDate = returnDate ? new Date(returnDate) : undefined;
      if (newReturnDate && returnDate) {
        newReturnDate.setDate(new Date(returnDate).getDate() - i);
      }

      backupSearches.push({
        searchParams: {
          ...originalParams,
          departureDate: newDepartureDate.toISOString().split('T')[0],
          returnDate: newReturnDate?.toISOString().split('T')[0],
        },
        modifications: [`Departure ${i} day${i > 1 ? 's' : ''} earlier`],
        reason: 'Different dates may have more flight availability',
      });
    }

    // Try departing 1-3 days later
    for (let i = 1; i <= expandDateRange; i++) {
      const newDepartureDate = new Date(departureDate);
      newDepartureDate.setDate(departureDate.getDate() + i);

      const newReturnDate = returnDate ? new Date(returnDate) : undefined;
      if (newReturnDate && returnDate) {
        newReturnDate.setDate(new Date(returnDate).getDate() + i);
      }

      backupSearches.push({
        searchParams: {
          ...originalParams,
          departureDate: newDepartureDate.toISOString().split('T')[0],
          returnDate: newReturnDate?.toISOString().split('T')[0],
        },
        modifications: [`Departure ${i} day${i > 1 ? 's' : ''} later`],
        reason: 'Different dates may have more flight availability',
      });
    }
  }

  // 3. Try with both relaxed cabin class and expanded dates
  if (relaxCabinClass && expandDates && originalParams.cabinClass === 'economy') {
    const departureDate = new Date(originalParams.departureDate);
    const returnDate = originalParams.returnDate ? new Date(originalParams.returnDate) : null;

    // Try 1 day earlier with premium economy
    const newDepartureDate = new Date(departureDate);
    newDepartureDate.setDate(departureDate.getDate() - 1);

    const newReturnDate = returnDate ? new Date(returnDate) : undefined;
    if (newReturnDate && returnDate) {
      newReturnDate.setDate(new Date(returnDate).getDate() - 1);
    }

    backupSearches.push({
      searchParams: {
        ...originalParams,
        cabinClass: 'premium_economy',
        departureDate: newDepartureDate.toISOString().split('T')[0],
        returnDate: newReturnDate?.toISOString().split('T')[0],
      },
      modifications: ['Upgraded to Premium Economy', 'Departure 1 day earlier'],
      reason: 'Combination of better cabin class and flexible dates',
    });
  }

  // 4. Try with nearby airports (if enabled and we have airport data)
  if (includeNearbyAirports) {
    try {
      const nearbyOrigins = await getNearbyAirports(originalParams.origin);
      const nearbyDestinations = await getNearbyAirports(originalParams.destination);

      // Try with nearby origin airports
      for (const nearbyOrigin of nearbyOrigins.slice(0, 2)) {
        // Limit to 2 nearby airports
        if (nearbyOrigin.iata_code !== originalParams.origin) {
          backupSearches.push({
            searchParams: {
              ...originalParams,
              origin: nearbyOrigin.iata_code,
            },
            modifications: [`Origin changed to ${nearbyOrigin.name} (${nearbyOrigin.iata_code})`],
            reason: 'Nearby airports may have more flight options',
          });
        }
      }

      // Try with nearby destination airports
      for (const nearbyDestination of nearbyDestinations.slice(0, 2)) {
        // Limit to 2 nearby airports
        if (nearbyDestination.iata_code !== originalParams.destination) {
          backupSearches.push({
            searchParams: {
              ...originalParams,
              destination: nearbyDestination.iata_code,
            },
            modifications: [
              `Destination changed to ${nearbyDestination.name} (${nearbyDestination.iata_code})`,
            ],
            reason: 'Nearby airports may have more flight options',
          });
        }
      }
    } catch (error) {
      console.warn('Failed to get nearby airports for backup search:', error);
    }
  }

  // Limit to top 8 backup searches to avoid overwhelming the user
  return backupSearches.slice(0, 8);
}

/**
 * Get nearby airports within the same city or region
 */
async function getNearbyAirports(airportCode: string): Promise<Airport[]> {
  try {
    // First get the airport details
    const { data: airport, error: airportError } = await supabase
      .from('airports')
      .select('iata_code, name, city_name')
      .eq('iata_code', airportCode)
      .single();

    if (airportError || !airport) {
      return [];
    }

    const airportData = airport as Airport;

    // Find airports in the same city
    const { data: nearbyAirports, error: nearbyError } = await supabase
      .from('airports')
      .select('iata_code, name, city_name')
      .eq('city_name', airportData.city_name)
      .neq('iata_code', airportCode)
      .limit(5);

    if (nearbyError) {
      return [];
    }

    return (nearbyAirports || []) as Airport[];
  } catch (error) {
    console.warn('Error fetching nearby airports:', error);
    return [];
  }
}

/**
 * Format backup search modifications for display
 */
export function formatBackupSearchDescription(result: BackupSearchResult): string {
  const modifications = result.modifications.join(', ');
  return `${modifications} - ${result.reason}`;
}
