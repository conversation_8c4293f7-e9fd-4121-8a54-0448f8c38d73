{"name": "gojumpingjack", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "type-check": "tsc --noEmit", "analyze": "cross-env ANALYZE=true next build", "db:migrate": "supabase db push", "db:reset": "supabase db reset", "prepare": "husky"}, "dependencies": {"@duffel/api": "^4.12.1", "@supabase/supabase-js": "^2.49.4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "geist": "^1.3.1", "intl-tel-input": "^25.3.1", "jsonwebtoken": "^9.0.2", "mailersend": "^2.6.0", "next": "15.2.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "zod": "^3.22.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@playwright/test": "^1.40.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.1", "@types/intl-tel-input": "^18.1.4", "@types/jest": "^29.5.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.5", "husky": "^9.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "prettier": "^3.1.0", "tailwindcss": "^4", "ts-jest": "^29.1.2", "typescript": "^5"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run type-check && npm test"}}}