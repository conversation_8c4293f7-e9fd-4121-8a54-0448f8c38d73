// Fare type explanations and utilities

export interface FareTypeInfo {
  name: string;
  description: string;
  features: string[];
  restrictions: string[];
}

/**
 * Get fare type information and explanation
 */
export function getFareTypeInfo(fareTypeName: string): FareTypeInfo {
  const fareType = fareTypeName.toLowerCase();

  // Common fare type mappings
  const fareTypeMap: Record<string, FareTypeInfo> = {
    basic: {
      name: 'Basic Fare',
      description: 'Our most affordable option with essential travel features.',
      features: ['Seat assignment at check-in', 'Standard carry-on bag', 'In-flight refreshments'],
      restrictions: ['No changes allowed', 'Non-refundable', 'No seat selection in advance'],
    },
    'basic economy': {
      name: 'Basic Economy',
      description: 'Budget-friendly fare with limited flexibility and services.',
      features: ['Personal item included', 'Standard carry-on bag'],
      restrictions: [
        'No changes or refunds',
        'No advance seat selection',
        'Boards last',
        'No upgrades',
      ],
    },
    economy: {
      name: 'Economy',
      description: 'Standard economy class with good value and flexibility.',
      features: [
        'Advance seat selection',
        'Standard carry-on and personal item',
        'In-flight entertainment',
        'Meals on long-haul flights',
      ],
      restrictions: ['Change fees may apply', 'Limited refund options'],
    },
    'premium economy': {
      name: 'Premium Economy',
      description: 'Enhanced comfort with extra legroom and premium services.',
      features: [
        'Extra legroom',
        'Priority boarding',
        'Enhanced meal service',
        'Premium entertainment',
        'Free seat selection',
      ],
      restrictions: ['Higher change fees', 'Limited availability'],
    },
    business: {
      name: 'Business Class',
      description: 'Premium travel experience with luxury amenities.',
      features: [
        'Lie-flat seats',
        'Priority check-in and boarding',
        'Lounge access',
        'Premium dining',
        'Extra baggage allowance',
      ],
      restrictions: ['Premium pricing', 'Limited availability'],
    },
    first: {
      name: 'First Class',
      description: 'Ultimate luxury travel experience.',
      features: [
        'Private suites',
        'Dedicated cabin crew',
        'Fine dining',
        'Premium lounge access',
        'Chauffeur service',
      ],
      restrictions: ['Highest pricing tier', 'Very limited availability'],
    },
  };

  // Try to match the fare type - exact match first, then partial match
  if (fareTypeMap[fareType]) {
    return fareTypeMap[fareType];
  }

  // Try partial matching
  for (const [key, info] of Object.entries(fareTypeMap)) {
    if (fareType.includes(key) || key.includes(fareType)) {
      return info;
    }
  }

  // Default fallback
  return {
    name: fareTypeName,
    description: 'Standard fare with airline-specific terms and conditions.',
    features: ['Standard airline services', 'Subject to fare rules'],
    restrictions: ['Terms and conditions apply', 'Check airline policy for details'],
  };
}

/**
 * Format fare type tooltip content
 */
export function formatFareTypeTooltip(fareTypeName: string): string {
  const info = getFareTypeInfo(fareTypeName);

  let tooltip = `${info.description}\n\n`;

  if (info.features.length > 0) {
    tooltip += 'Includes:\n';
    info.features.forEach(feature => {
      tooltip += `• ${feature}\n`;
    });
  }

  if (info.restrictions.length > 0) {
    tooltip += '\nRestrictions:\n';
    info.restrictions.forEach(restriction => {
      tooltip += `• ${restriction}\n`;
    });
  }

  return tooltip.trim();
}
