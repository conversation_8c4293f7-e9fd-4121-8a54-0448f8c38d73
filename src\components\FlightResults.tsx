'use client'; // Required for useState, useEffect

import React, { useState, useEffect, useCallback, useRef } from 'react';
import type { SearchParamsType, Flight } from '@/types'; // Import shared types
import type { FlightSearchParams } from '@/hooks/useDuffelFlightSearch';
import FlightCard from './FlightCard'; // <--- ADD BACK FlightCard Import
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabaseClient';
import { generateBackupSearches, BackupSearchResult } from '@/lib/backupSearch';

// Type definitions for Duffel API responses
interface DuffelOffer {
  id: string;
  total_amount: string;
  total_currency: string;
  owner: { name: string };
  slices: DuffelSlice[];
}

interface DuffelSlice {
  segments: DuffelSegment[];
}

interface DuffelSegment {
  origin: { iata_code: string };
  destination: { iata_code: string };
  departing_at: string;
  arriving_at: string;
  duration: string;
  marketing_carrier: { name: string };
  marketing_carrier_flight_number: string;
  operating_carrier?: { name: string };
  operating_carrier_flight_number?: string;
  aircraft: string;
  passengers: Array<{ cabin_class: string }>;
}

// --- Component Props Interface ---
interface FlightResultsProps {
  searchParams: SearchParamsType[];
  showPagination?: boolean;
  onPageChange?: (page: number) => void;
  currentPage?: number;
  filterFlights?: (flights: Flight[]) => Flight[];
  sortBy?: string; // Add sortBy prop
}

const JACK_VIDEO_PATH = '/Jack_Finding_Flights.mp4';

// --- Flight Results Component ---
const FlightResults: React.FC<FlightResultsProps> = ({
  searchParams,
  showPagination = false,
  onPageChange,
  currentPage = 1,
  filterFlights,
  sortBy = 'price', // Default to price sorting
}) => {
  const router = useRouter();
  const [allOffers, setAllOffers] = useState<Flight[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [backupSearches, setBackupSearches] = useState<BackupSearchResult[]>([]);
  const [showBackupSearches, setShowBackupSearches] = useState(false);
  const [backupSearchLoading, setBackupSearchLoading] = useState(false);
  // --- Loading phrases state ---
  const [phrases, setPhrases] = useState<string[]>([]);
  // --- Ref to track last search parameters ---
  const lastSearchParams = useRef<SearchParamsType[] | null>(null);
  const [phraseIndex, setPhraseIndex] = useState(0);
  const videoRef = useRef<HTMLVideoElement>(null);
  const lastTimeRef = useRef(0);

  // Fetch loading phrases from Supabase on mount
  useEffect(() => {
    async function fetchPhrases() {
      const { data, error } = await supabase.from('loading_phrases').select('phrase');
      console.log('[FlightResults] loading_phrases data:', data, 'error:', error);
      if (error || !data || data.length === 0) {
        setPhrases(['Finding the best flights...', 'Jack is searching for deals...']);
        setPhraseIndex(Math.floor(Math.random() * 2));
      } else {
        setPhrases(data.map((row: { phrase: unknown }) => String(row.phrase)));
        // Set a random initial phrase index
        const max = data.length;
        setPhraseIndex(Math.floor(Math.random() * max));
      }
    }
    fetchPhrases();
  }, []);

  // Handler for when the video loops (using onTimeUpdate)
  const handleVideoTimeUpdate = () => {
    const video = videoRef.current;
    if (!video || phrases.length === 0) return;
    const currentTime = video.currentTime;
    if (currentTime < lastTimeRef.current) {
      // Video looped
      let nextIndex = phraseIndex;
      let tries = 0;
      while (phrases.length > 1 && nextIndex === phraseIndex && tries < 10) {
        nextIndex = Math.floor(Math.random() * phrases.length);
        tries++;
      }
      setPhraseIndex(nextIndex);
    }
    lastTimeRef.current = currentTime;
  };

  // Helper: Filter valid flights (not partial, has outbound segments)
  // function filterValidFlights(flights: unknown[]): unknown[] {
  //   return flights.filter(
  //     flight =>
  //       !flight.partial &&
  //       Array.isArray(flight.outbound_segments) &&
  //       flight.outbound_segments.length > 0
  //   );
  // }

  // Helper: Adjust date by days
  // function adjustDate(dateStr: string, days: number): string {
  //   const date = new Date(dateStr);
  //   date.setDate(date.getDate() + days);
  //   return date.toISOString().split('T')[0];
  // }

  // Helper: Get all cabin classes
  // const CABIN_CLASSES = ['economy', 'premium_economy', 'business', 'first'];

  // Helper: Try city search (if not already city)
  // function isAirportCode(code: string) {
  //   return code && code.length === 3 && code.toUpperCase() === code;
  // }

  // Helper to map SearchParamsType to FlightSearchParams
  function toFlightSearchParams(params: SearchParamsType): FlightSearchParams {
    return {
      origin: params.originAirport,
      destination: params.destinationAirport,
      departureDate: params.departureDate,
      returnDate: params.returnDate,
      passengers: {
        adults: params.adults,
        children: params.children,
        infants: params.infants,
      },
      cabinClass: params.cabinClass || 'economy',
    };
  }

  // Helper to subscribe to a job's results
  function subscribeToJob(jobId: string, onOffers: (offers: Flight[]) => void) {
    const channelName = `duffel_job_${jobId}`;
    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'duffel_jobs',
          filter: `id=eq.${jobId}`,
        },
        payload => {
          if (payload?.new?.results_data?.data) {
            onOffers(payload.new.results_data.data);
          }
        }
      )
      .subscribe();
    return () => supabase.removeChannel(channel);
  }

  // Helper: Get the most common or lowest cabin class from all segments
  function deriveCabinClass(slices: DuffelSlice[]): string {
    const allCabins: string[] = [];
    if (Array.isArray(slices)) {
      slices.forEach(slice => {
        if (Array.isArray(slice?.segments)) {
          slice.segments.forEach((segment: DuffelSegment) => {
            if (Array.isArray(segment?.passengers) && segment.passengers[0]?.cabin_class) {
              allCabins.push(segment.passengers[0].cabin_class);
            }
          });
        }
      });
    }
    if (allCabins.length === 0) return 'unknown';
    const uniqueCabins = Array.from(new Set(allCabins));
    if (uniqueCabins.length === 1) return uniqueCabins[0];
    // Optionally, sort by class hierarchy if you want the lowest
    const hierarchy = ['economy', 'premium_economy', 'business', 'first'];
    const sorted = uniqueCabins.sort((a, b) => hierarchy.indexOf(a) - hierarchy.indexOf(b));
    return 'mixed (' + sorted.join(', ') + ')';
  }

  // Helper: Transform Duffel offer to Flight shape
  const duffelOfferToFlight = useCallback((offer: unknown): Flight | null => {
    try {
      const offerObj = offer as DuffelOffer;
      // Defensive: check for required fields
      if (!offerObj?.slices?.[0]?.segments?.[0]) {
        console.warn('Skipping offer with missing segments:', offer);
        return null;
      }

      const outboundSegments = offerObj.slices[0].segments || [];
      const returnSegments = offerObj.slices[1]?.segments || [];
      const flightCabinClass = deriveCabinClass(offerObj.slices || []);

      // Helper function to create a segment with proper field names
      const createSegment = (segment: DuffelSegment) => {
        const segmentData = {
          origin_airport: segment.origin?.iata_code || '',
          destination_airport: segment.destination?.iata_code || '',
          departure_at: segment.departing_at || '',
          arrival_at: segment.arriving_at || '',
          duration: segment.duration || '',
          airline: segment.operating_carrier?.name || segment.marketing_carrier?.name || '',
          flight_number:
            segment.operating_carrier_flight_number || segment.marketing_carrier_flight_number || '',
          aircraft: segment.aircraft || '',
          cabin_class: segment.passengers?.[0]?.cabin_class || 'economy',
        };

        // Note: This segment is part of a complete journey

        return segmentData;
      };

      const processedOutbound = Array.isArray(outboundSegments)
        ? outboundSegments.map((seg: DuffelSegment) => createSegment(seg))
        : [];
      const processedReturn = Array.isArray(returnSegments)
        ? returnSegments.map((seg: DuffelSegment) => createSegment(seg))
        : [];

      // Log the complete journey for debugging
      if (processedOutbound.length > 1) {
        console.log(`[FlightResults] Multi-segment journey: ${processedOutbound[0].origin_airport} → ${processedOutbound.map(s => s.destination_airport).join(' → ')} (${processedOutbound.length} segments)`);
      }

      return {
        airline: offerObj.owner?.name || 'Unknown',
        price: Number(offerObj.total_amount),
        link: offerObj.id,
        stops: outboundSegments.length > 0 ? outboundSegments.length - 1 : 0,
        cabin_class: flightCabinClass,
        currency: offerObj.total_currency || 'USD',
        outbound_segments: processedOutbound,
        return_segments: processedReturn,
      };
    } catch (err) {
      console.error('Error transforming Duffel offer:', err);
      return null;
    }
  }, []);

  const handleSeeAllFlights = () => {
    if (!searchParams || searchParams.length === 0) return;
    // Serialize all searchParams as JSON and encode
    const allParams = encodeURIComponent(JSON.stringify(searchParams));
    router.push(`/flights?allSearchParams=${allParams}`);
  };

  // Helper function to calculate total duration in minutes
  const calculateTotalDuration = (flight: Flight) => {
    if (!Array.isArray(flight.outbound_segments) || !flight.outbound_segments.length) return 0;
    const firstSegment = flight.outbound_segments[0];
    const lastSegment = flight.outbound_segments[flight.outbound_segments.length - 1];
    const start = new Date(firstSegment?.departure_at);
    const end = new Date(lastSegment?.arrival_at);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) return 0;
    return end.getTime() - start.getTime();
  };

  // Filtering, sorting, and pagination logic (client-side)
  let sortedFlights = Array.isArray(allOffers) ? [...allOffers] : [];

  // Apply sorting based on sortBy value
  sortedFlights.sort((a, b) => {
    switch (sortBy) {
      case 'duration':
        return calculateTotalDuration(a) - calculateTotalDuration(b);
      case 'departure':
        const aTime = new Date(a.outbound_segments[0]?.departure_at).getTime();
        const bTime = new Date(b.outbound_segments[0]?.departure_at).getTime();
        return aTime - bTime;
      case 'price':
      default:
        return a.price - b.price;
    }
  });

  if (filterFlights) sortedFlights = filterFlights(sortedFlights);
  const displayedFlights = showPagination ? sortedFlights : sortedFlights.slice(0, 3);
  const totalResults = sortedFlights.length;
  const totalPages = Math.ceil(totalResults / 10);
  console.log(
    '[FlightResults] searchParams.length:',
    searchParams.length,
    'allOffers.length:',
    allOffers.length,
    'displayedFlights.length:',
    displayedFlights.length,
    'showPagination:',
    showPagination
  );

  useEffect(() => {
    if (!Array.isArray(searchParams) || searchParams.length === 0) return;

    // Debug logging for search parameters
    console.log('[FlightResults] Starting search with parameters:', searchParams);
    searchParams.forEach((params, index) => {
      console.log(`[FlightResults] Search ${index + 1}:`, {
        origin: params.originAirport,
        destination: params.destinationAirport,
        departure: params.departureDate,
        return: params.returnDate,
        adults: params.adults
      });
    });

    // Check if we already have results for these exact search parameters
    const currentSearchKey = JSON.stringify(searchParams);
    const lastSearchKey = JSON.stringify(lastSearchParams.current);

    // If search parameters haven't changed and we have results, don't perform new search
    if (currentSearchKey === lastSearchKey && allOffers.length > 0) {
      console.log('[FlightResults] Search parameters unchanged and results exist, skipping new search');
      setLoading(false);
      setError(null);
      return;
    }

    // Store current search parameters for comparison
    lastSearchParams.current = searchParams;

    setLoading(true);
    setError(null);
    // Only clear offers if we're doing a genuinely new search
    if (currentSearchKey !== lastSearchKey) {
      setAllOffers([]);
    }
    let isCancelled = false;
    const unsubscribers: (() => void)[] = [];
    const allOffersMap = new Map();
    let completedJobs = 0;
    const totalJobs = searchParams.length;

    // Increase timeout to 90 seconds for multiple airport searches
    const safetyTimeout = setTimeout(() => {
      if (isCancelled) return;
      setLoading(false);
      if (allOffersMap.size === 0) {
        setError('Search timed out. This can happen during peak times or with complex searches. Please try again or modify your search criteria.');
      } else {
        console.warn('Search partially completed - some results may be missing');
      }
    }, 90000);

    (async () => {
      try {
        await Promise.all(
          (Array.isArray(searchParams) ? searchParams : []).map(async params => {
            try {
              if (isCancelled) return;

              const flightParams = toFlightSearchParams(params);
              // Call initiateSearch and get jobId
              const { data, error } = await supabase.functions.invoke('initiate-duffel-search', {
                body: { searchParams: flightParams },
              });

              if (error) {
                console.error('Error initiating search:', error);
                completedJobs++;
                if (completedJobs === totalJobs) {
                  clearTimeout(safetyTimeout);
                  setLoading(false);
                  if (allOffersMap.size === 0) {
                    setError('Failed to search for flights. Please try again.');
                  }
                }
                return;
              }

              if (!data?.job_id) {
                console.error('No job ID returned from search');
                completedJobs++;
                if (completedJobs === totalJobs) {
                  clearTimeout(safetyTimeout);
                  setLoading(false);
                  if (allOffersMap.size === 0) {
                    setError('Failed to start flight search. Please try again.');
                  }
                }
                return;
              }

              const jobId = data.job_id;
              // Subscribe to this job's results
              const unsubscribe = subscribeToJob(jobId, offers => {
                if (isCancelled) return;
                if (Array.isArray(offers)) {
                  for (const offer of offers) {
                    const transformedFlight = duffelOfferToFlight(offer);
                    if (transformedFlight) {
                      allOffersMap.set((offer as unknown as DuffelOffer).id, transformedFlight);
                    }
                  }
                }
                setAllOffers(Array.from(allOffersMap.values()));
                console.log(
                  '[FlightResults] setAllOffers called. allOffersMap size:',
                  allOffersMap.size
                );
                completedJobs++;
                if (completedJobs === totalJobs) {
                  clearTimeout(safetyTimeout);
                  setLoading(false);
                }
              });
              unsubscribers.push(unsubscribe);
            } catch (err: unknown) {
              if ((err as { response?: { status?: number } })?.response?.status === 400) {
                console.error('400 Bad Request:', err);
                setError(
                  'There was a problem with your search. Please check your airport selections and try again.'
                );
              } else {
                console.error('Error fetching flight results:', err);
              }
              completedJobs++;
              if (completedJobs >= totalJobs && !isCancelled) {
                setLoading(false);
              }
            }
          })
        );
      } catch (err) {
        console.error('Unexpected error in fetchAll:', err);
        setError('An unexpected error occurred. Please try again.');
        setLoading(false);
      }
    })();

    return () => {
      isCancelled = true;
      clearTimeout(safetyTimeout);
      if (Array.isArray(unsubscribers)) {
        unsubscribers.forEach(unsub => unsub());
      }
    };
  }, [searchParams, duffelOfferToFlight]);

  // Generate backup searches when no results are found
  const handleGenerateBackupSearches = useCallback(async () => {
    if (!searchParams || searchParams.length === 0) return;

    setBackupSearchLoading(true);
    try {
      const originalParams = toFlightSearchParams(searchParams[0]);
      const backupResults = await generateBackupSearches(originalParams, {
        relaxCabinClass: true,
        expandDates: true,
        expandDateRange: 3,
        includeNearbyAirports: false, // Disable for now to avoid complexity
      });
      setBackupSearches(backupResults);
      setShowBackupSearches(true);
    } catch (error) {
      console.error('Failed to generate backup searches:', error);
    } finally {
      setBackupSearchLoading(false);
    }
  }, [searchParams]);

  // Handle backup search selection
  const handleBackupSearchSelect = useCallback(
    (backupResult: BackupSearchResult) => {
      // Convert back to SearchParamsType format
      const newSearchParams: SearchParamsType = {
        originAirport: backupResult.searchParams.origin,
        destinationAirport: backupResult.searchParams.destination,
        departureDate: backupResult.searchParams.departureDate,
        returnDate: backupResult.searchParams.returnDate,
        adults: backupResult.searchParams.passengers.adults,
        children: backupResult.searchParams.passengers.children || 0,
        infants: backupResult.searchParams.passengers.infants || 0,
        cabinClass: backupResult.searchParams.cabinClass,
      };

      // Navigate to new search
      const params = new URLSearchParams({
        allSearchParams: JSON.stringify([newSearchParams]),
      });
      router.push(`/flights?${params.toString()}`);
    },
    [router]
  );

  // Render logic (reuse existing error/loading/empty states)
  if (loading) {
    return (
      <section id="flight-results" className="py-8 md:py-12 bg-white scroll-mt-24">
        <div className="container mx-auto px-4 text-center flex flex-col items-center justify-center">
          <video
            ref={videoRef}
            src={JACK_VIDEO_PATH}
            width={640}
            height={360}
            autoPlay
            loop
            muted
            playsInline
            onTimeUpdate={handleVideoTimeUpdate}
            className="rounded-lg shadow-lg mb-4"
            style={{ maxWidth: 800 }}
          />
          <div className="text-lg font-semibold text-blue-700 min-h-[2.5rem]">
            {phrases.length > 0 ? phrases[phraseIndex] : 'Finding the best flights...'}
          </div>
        </div>
      </section>
    );
  }
  if (error) {
    return (
      <section id="flight-results" className="py-8 md:py-12 bg-white scroll-mt-24">
        <div className="container mx-auto px-4">
          <div className="text-center text-red-700 bg-red-100 p-4 rounded border border-red-300 max-w-md mx-auto">
            <p className="font-semibold text-lg mb-1">Oops! Couldn&apos;t Load Flights</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      </section>
    );
  }
  if (!searchParams) {
    return null;
  }
  if (allOffers.length === 0) {
    return (
      <section id="flight-results" className="py-8 md:py-12 bg-white scroll-mt-24">
        <div className="container mx-auto px-4">
          <div className="text-center text-gray-600 py-10 bg-gray-50 rounded-lg max-w-2xl mx-auto">
            <p className="text-xl mb-2 font-medium">No flights found matching your criteria.</p>
            <p className="text-sm mb-6">Don&apos;t worry! We can help you find alternatives.</p>

            {!showBackupSearches && (
              <button
                onClick={handleGenerateBackupSearches}
                disabled={backupSearchLoading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
              >
                {backupSearchLoading ? 'Finding alternatives...' : 'Show Alternative Options'}
              </button>
            )}

            {showBackupSearches && backupSearches.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  Try these alternatives:
                </h3>
                <div className="space-y-3">
                  {backupSearches.map((backup, index) => (
                    <div
                      key={index}
                      className="bg-white border border-gray-200 rounded-lg p-4 text-left hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                      onClick={() => handleBackupSearchSelect(backup)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-800">
                            {backup.modifications.join(', ')}
                          </p>
                          <p className="text-sm text-gray-600 mt-1">{backup.reason}</p>
                        </div>
                        <button className="text-blue-600 hover:text-blue-700 font-medium text-sm">
                          Search →
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-4">
                  Click any option above to search with those modifications
                </p>
              </div>
            )}

            {showBackupSearches && backupSearches.length === 0 && !backupSearchLoading && (
              <div className="mt-6">
                <p className="text-sm text-gray-600">
                  We couldn&apos;t find any alternative options. Try adjusting your search criteria
                  manually.
                </p>
              </div>
            )}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="flight-results" className="py-8 md:py-12 bg-white scroll-mt-24">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-semibold text-gray-800 mb-6 text-center md:text-left font-serif">
          {showPagination ? 'All Flight Deals' : 'Top Flight Deals Found'}
        </h2>
        <div className="space-y-4 max-w-4xl mx-auto">
          {Array.isArray(displayedFlights) && displayedFlights.length > 0 ? (
            displayedFlights.map((flight, index) => {
              // Convert SearchParamsType to Record<string, string | number>
              const convertedSearchParams = searchParams && searchParams.length > 0 ? {
                originAirport: searchParams[0].originAirport || '',
                destinationAirport: searchParams[0].destinationAirport || '',
                departureDate: searchParams[0].departureDate || '',
                returnDate: searchParams[0].returnDate || '',
                adults: searchParams[0].adults || 1,
                children: searchParams[0].children || 0,
                infants: searchParams[0].infants || 0,
                cabinClass: searchParams[0].cabinClass || 'economy',
              } : undefined;

              return (
                <FlightCard
                  key={flight.link ? `${flight.link}-${index}` : `flight-home-${index}`}
                  flight={flight}
                  searchParams={convertedSearchParams}
                />
              );
            })
          ) : (
            <div className="text-center text-gray-600 py-10">No flights to display.</div>
          )}

          {/* "See More Results" Button */}
          {!showPagination && totalResults > 3 && (
            <div className="text-center pt-6">
              <button
                onClick={handleSeeAllFlights}
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                See All {totalResults} Flights
                <svg
                  className="ml-2 -mr-1 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          )}

          {/* Pagination Controls */}
          {showPagination && totalPages > 1 && (
            <div className="flex justify-center space-x-2 mt-6">
              <button
                onClick={() => onPageChange?.(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-4 py-2 border rounded disabled:opacity-50"
              >
                Previous
              </button>
              <span className="px-4 py-2">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => onPageChange?.(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-4 py-2 border rounded disabled:opacity-50"
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default FlightResults;
