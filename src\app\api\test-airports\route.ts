import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Check if airports table has data and specifically check for MCO and LAX
    const { data: airports, error } = await supabase
      .from('airports')
      .select('iata_code, duffel_id, name')
      .in('iata_code', ['MCO', 'LAX', 'JFK'])
      .order('iata_code');

    if (error) {
      throw new Error(error.message);
    }

    // Get total count of airports
    const { count, error: countError } = await supabase
      .from('airports')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      throw new Error(countError.message);
    }

    return NextResponse.json({
      success: true,
      totalAirports: count,
      testAirports: airports,
      hasDuffelIds: airports?.every(airport => airport.duffel_id) || false,
    });
  } catch (error: any) {
    console.error('Error checking airports:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to check airports',
      },
      { status: 500 }
    );
  }
}
