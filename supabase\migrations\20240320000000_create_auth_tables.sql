-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name <PERSON><PERSON><PERSON><PERSON>(255),
  last_name <PERSON><PERSON><PERSON><PERSON>(255),
  date_of_birth DATE,
  phone_number VA<PERSON>HA<PERSON>(50),
  site_rewards_tokens INTEGER DEFAULT 0,
  home_airport_iata_code CHAR(3),
  avoided_airline_iata_codes CHAR(2)[],
  default_cabin_class VARCHAR(20),
  default_adult_passengers INTEGER,
  default_child_passengers INTEGER,
  default_infant_passengers INTEGER,
  loyalty_programs JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  email_verified BOOLEAN DEFAULT FALSE,
  reset_password_token VARCHAR(255),
  reset_password_expires TIMESTAMP WITH TIME ZONE
);

-- Create airline_cache table
CREATE TABLE airline_cache (
  id INTEGER PRIMARY KEY,
  data JSONB NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_reset_password_token ON users(reset_password_token);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_users_updated_at
  BEFORE UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column(); 