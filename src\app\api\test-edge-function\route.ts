import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    console.log('Testing Edge Function call...');

    // Test the initiate-duffel-search function with a simple request
    const { data, error } = await supabase.functions.invoke('initiate-duffel-search', {
      body: { 
        searchParams: {
          origin: 'MCO',
          destination: 'LAX',
          departureDate: '2025-07-21',
          adults: 1,
          cabinClass: 'economy'
        }
      },
    });

    console.log('Edge Function response:', { data, error });

    return NextResponse.json({
      success: !error,
      data: data,
      error: error?.message || null,
      message: error ? 'Edge Function failed' : 'Edge Function working'
    });
  } catch (error: any) {
    console.error('Error testing Edge Function:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to test Edge Function',
      },
      { status: 500 }
    );
  }
}
