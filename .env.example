# GoJumpingJack Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# REQUIRED ENVIRONMENT VARIABLES
# =============================================================================

# Duffel API Configuration
# Get your API keys from https://duffel.com/
DUFFEL_TOKEN=your_duffel_api_token_here
DUFFEL_MODE=test
# Optional: Separate tokens for different environments
DUFFEL_TEST_TOKEN=your_duffel_test_token_here
DUFFEL_LIVE_TOKEN=your_duffel_live_token_here

# Supabase Configuration
# Get these from your Supabase project settings
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Authentication
# Generate a strong secret key for JWT tokens (use: openssl rand -base64 32)
JWT_SECRET=your_very_secure_jwt_secret_key_here

# Email Service (MailerSend)
# Get your API key from https://www.mailersend.com/
MAILERSEND_PROD_TOKEN=your_mailersend_api_token_here
VERIFICATION_EMAIL_FROM=<EMAIL>

# Application Base URL
NEXT_PUBLIC_BASE_URL=https://www.gojumpingjack.com

# =============================================================================
# OPTIONAL ENVIRONMENT VARIABLES
# =============================================================================

# Unsplash API (for destination images)
# Get your access key from https://unsplash.com/developers
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# QStash Configuration (for background job processing)
# Get these from https://console.upstash.com/
QSTASH_CURRENT_SIGNING_KEY=your_qstash_current_signing_key_here
QSTASH_NEXT_SIGNING_KEY=your_qstash_next_signing_key_here
QSTASH_TOKEN=your_qstash_token_here

# Vercel Cron Secret (for scheduled tasks)
# Generate a random string for securing cron endpoints
VERCEL_CRON_SECRET=your_vercel_cron_secret_here

# =============================================================================
# DEVELOPMENT ENVIRONMENT VARIABLES
# =============================================================================

# Application Environment
NODE_ENV=development

# Logging Configuration
LOG_LEVEL=debug

# Database Configuration (if using local Supabase)
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres

# Redis Configuration (if implementing caching)
REDIS_URL=redis://localhost:6379

# =============================================================================
# MONITORING & ANALYTICS (Optional)
# =============================================================================

# Sentry (Error Tracking)
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ORG=your_sentry_org_here
SENTRY_PROJECT=your_sentry_project_here

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# PostHog (Product Analytics)
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# =============================================================================
# THIRD-PARTY INTEGRATIONS (Optional)
# =============================================================================

# Email Service (for notifications)
SENDGRID_API_KEY=your_sendgrid_api_key_here
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password_here

# Payment Processing (if implementing payments)
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret_here

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,https://www.gojumpingjack.com

# Rate Limiting
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Session Configuration
SESSION_SECRET=your_session_secret_here
SESSION_MAX_AGE=604800

# =============================================================================
# FEATURE FLAGS (Optional)
# =============================================================================

# Enable/disable features
FEATURE_LOYALTY_PROGRAMS=true
FEATURE_REWARDS_SYSTEM=true
FEATURE_PUSH_NOTIFICATIONS=false
FEATURE_OFFLINE_MODE=false

# =============================================================================
# NOTES
# =============================================================================

# 1. Never commit this file with real values to version control
# 2. Use strong, unique passwords and secrets
# 3. Rotate secrets regularly
# 4. Use different values for development, staging, and production
# 5. Consider using a secret management service for production

# For production deployment on Vercel:
# - Set these variables in your Vercel project settings
# - Use Vercel's environment variable encryption
# - Consider using Vercel's integration with secret management services

# For local development:
# - Copy this file to .env.local
# - Fill in your actual values
# - .env.local is already in .gitignore and won't be committed
