// Test file for the flight booking interface improvements

import { getTimeUntilExpiration } from '@/lib/dateUtils';
import { getFareTypeInfo, formatFareTypeTooltip } from '@/lib/fareTypeUtils';
import { getCategorizedAmenities, ProcessedAmenities } from '@/lib/amenitiesUtils';

describe('Flight Booking Interface Improvements', () => {
  describe('Countdown Timer Enhancement', () => {
    test('should format countdown with Hours and Minutes labels', () => {
      // Create a future date (2 hours and 30 minutes from now)
      const futureDate = new Date();
      futureDate.setHours(futureDate.getHours() + 2);
      futureDate.setMinutes(futureDate.getMinutes() + 30);

      const result = getTimeUntilExpiration(futureDate.toISOString());

      expect(result.expired).toBe(false);
      expect(result.formatted).toContain('Hours');
      expect(result.formatted).toContain('Minutes');
    });

    test('should format countdown with only Minutes when less than 1 hour', () => {
      // Create a future date (30 minutes from now)
      const futureDate = new Date();
      futureDate.setMinutes(futureDate.getMinutes() + 30);

      const result = getTimeUntilExpiration(futureDate.toISOString());

      expect(result.expired).toBe(false);
      expect(result.formatted).toContain('Minutes');
      expect(result.formatted).not.toContain('Hours');
    });

    test('should handle expired dates', () => {
      // Create a past date
      const pastDate = new Date();
      pastDate.setHours(pastDate.getHours() - 1);

      const result = getTimeUntilExpiration(pastDate.toISOString());

      expect(result.expired).toBe(true);
    });
  });

  describe('Fare Type Tooltip', () => {
    test('should provide detailed info for Basic Economy', () => {
      const info = getFareTypeInfo('Basic Economy');

      expect(info.name).toBe('Basic Economy');
      expect(info.description).toContain('Budget-friendly');
      expect(info.restrictions).toContain('No changes or refunds');
    });

    test('should provide detailed info for Premium Economy', () => {
      const info = getFareTypeInfo('Premium Economy');

      expect(info.name).toBe('Premium Economy');
      expect(info.features).toContain('Extra legroom');
      expect(info.features).toContain('Priority boarding');
    });

    test('should format tooltip text correctly', () => {
      const tooltip = formatFareTypeTooltip('Basic Economy');

      expect(tooltip).toContain('Budget-friendly');
      expect(tooltip).toContain('Includes:');
      expect(tooltip).toContain('Restrictions:');
    });

    test('should handle unknown fare types', () => {
      const info = getFareTypeInfo('Unknown Fare Type');

      expect(info.name).toBe('Unknown Fare Type');
      expect(info.description).toContain('Standard fare');
    });
  });

  describe('Amenities Display Redesign', () => {
    test('should categorize amenities correctly', () => {
      const mockAmenities: ProcessedAmenities = {
        wifi: {
          available: true,
          cost: 'paid',
          icon: '📶',
          label: 'WiFi ($)',
        },
        power: {
          available: false,
          icon: '🚫',
          label: 'No power outlet',
        },
        seat: {
          pitch: '32 inches',
          icon: '💺',
          label: 'Standard seat',
        },
      };

      const result = getCategorizedAmenities(mockAmenities);

      expect(result.included).toHaveLength(2); // WiFi and Seat
      expect(result.notIncluded).toHaveLength(1); // Power

      // Check WiFi is marked as paid
      const wifiItem = result.included.find(item => item.label === 'WiFi');
      expect(wifiItem?.isPaid).toBe(true);

      // Check power is in not included
      expect(result.notIncluded[0].label).toBe('Power Outlet');
    });

    test('should handle all amenities available', () => {
      const mockAmenities: ProcessedAmenities = {
        wifi: {
          available: true,
          cost: 'free',
          icon: '📶',
          label: 'WiFi (Free)',
        },
        power: {
          available: true,
          icon: '🔌',
          label: 'Power outlet',
        },
        seat: {
          icon: '💺',
          label: 'Standard seat',
        },
      };

      const result = getCategorizedAmenities(mockAmenities);

      expect(result.included).toHaveLength(3);
      expect(result.notIncluded).toHaveLength(0);

      // Check WiFi is not marked as paid
      const wifiItem = result.included.find(item => item.label === 'WiFi');
      expect(wifiItem?.isPaid).toBe(false);
    });
  });
});
