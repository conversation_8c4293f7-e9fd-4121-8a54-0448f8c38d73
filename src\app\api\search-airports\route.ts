import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function GET(req: NextRequest) {
  const start = Date.now();
  logger.apiRequest('GET', '/api/search-airports');

  const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  logger.debug('Environment check', {
    hasSupabaseUrl: !!SUPABASE_URL,
    hasSupabaseKey: !!SUPABASE_ANON_KEY,
    component: 'search-airports',
  });

  if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
    logger.error('Missing Supabase credentials', undefined, { component: 'search-airports' });
    return NextResponse.json(
      { error: 'Server configuration error: Missing Supabase credentials.' },
      { status: 500 }
    );
  }

  const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
  logger.debug('Supabase client initialized', { component: 'search-airports' });

  const { searchParams } = new URL(req.url);
  const searchTerm = searchParams.get('q');

  logger.info('Airport search request', {
    searchTerm,
    component: 'search-airports',
  });

  if (!searchTerm || typeof searchTerm !== 'string' || searchTerm.trim().length < 2) {
    logger.debug('Search term too short or invalid', {
      searchTerm,
      component: 'search-airports',
    });
    const duration = Date.now() - start;
    logger.apiResponse('GET', '/api/search-airports', 200, duration);
    return NextResponse.json([]);
  }

  try {
    logger.debug('Executing Supabase query', {
      searchTerm,
      component: 'search-airports',
    });

    const { data: airports, error } = await supabase
      .from('airports')
      .select('*')
      .or(
        `iata_code.ilike.%${searchTerm}%,name.ilike.%${searchTerm}%,city_name.ilike.%${searchTerm}%`
      )
      .limit(10);

    if (error) {
      logger.error('Supabase query failed', error as Error, {
        searchTerm,
        component: 'search-airports',
      });
      const duration = Date.now() - start;
      logger.apiResponse('GET', '/api/search-airports', 500, duration);
      return NextResponse.json({ error: error.message, details: error }, { status: 500 });
    }

    interface AirportWithScore {
      duffel_id?: string;
      iata_code?: string;
      name?: string;
      city_name?: string;
      country_code?: string;
      latitude?: number;
      longitude?: number;
      time_zone?: string;
      type?: string;
      relevanceScore: number;
    }

    // Sort results to prioritize exact matches and filter out irrelevant results
    const sortedAirports = (airports || [])
      .map((airport: AirportWithScore) => {
        // Calculate relevance score
        let score = 0;
        const searchLower = searchTerm.toLowerCase();
        const cityLower = airport.city_name?.toLowerCase() || '';
        const nameLower = airport.name?.toLowerCase() || '';
        const codeLower = airport.iata_code?.toLowerCase() || '';

        // Exact matches get highest priority
        if (codeLower === searchLower) score += 1000;
        if (cityLower === searchLower) score += 900;
        if (nameLower === searchLower) score += 800;

        // Boost score for major airports
        if (airport.type === 'large_airport') score += 100;
        else if (airport.type === 'medium_airport') score += 50;

        // Starts with matches
        if (codeLower.startsWith(searchLower)) score += 700;
        if (cityLower.startsWith(searchLower)) score += 600;
        if (nameLower.startsWith(searchLower)) score += 500;

        // Contains matches (but only if search term is at least 3 characters)
        if (searchTerm.length >= 3) {
          if (cityLower.includes(searchLower)) score += 300;
          if (nameLower.includes(searchLower)) score += 200;
        }

        return { ...airport, relevanceScore: score };
      })
      .filter((airport: AirportWithScore) => airport.relevanceScore > 0) // Only include relevant results
      .sort((a: AirportWithScore, b: AirportWithScore) => b.relevanceScore - a.relevanceScore) // Sort by relevance
      // Remove duplicates with same IATA code, keeping the highest scored one
      .filter((airport: AirportWithScore, index: number, array: AirportWithScore[]) => {
        if (!airport.iata_code) return true;
        const firstIndex = array.findIndex(a => a.iata_code === airport.iata_code);
        return firstIndex === index;
      })
      .map(airport => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { relevanceScore, ...airportWithoutScore } = airport;
        return airportWithoutScore;
      }); // Remove score from final result

    logger.info('Airport search completed', {
      searchTerm,
      resultCount: sortedAirports.length,
      component: 'search-airports',
    });

    const duration = Date.now() - start;
    logger.apiResponse('GET', '/api/search-airports', 200, duration);

    return NextResponse.json(sortedAirports);
  } catch (err: unknown) {
    const duration = Date.now() - start;
    const error = err instanceof Error ? err : new Error('Unknown error occurred');
    logger.error('Unexpected error in airport search', error, {
      searchTerm,
      component: 'search-airports',
    });
    logger.apiResponse('GET', '/api/search-airports', 500, duration);

    return NextResponse.json(
      {
        error: error.message || 'Internal Server Error',
      },
      { status: 500 }
    );
  }
}
