import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import axios from 'axios';

async function fetchAllDuffelAirports(DUFFEL_TOKEN: string) {
  let airports: any[] = [];
  let after: string | null = null;
  const limit = 100;
  do {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (after) params.append('after', after);
    const response = await axios.get(`https://api.duffel.com/air/airports?${params.toString()}`, {
      headers: { Authorization: `Bearer ${DUFFEL_TOKEN}` },
    });
    airports = airports.concat(response.data.data);
    after = response.data.meta?.after || null;
  } while (after);
  return airports;
}

function mapDuffelToSupabase(airport: any) {
  return {
    duffel_id: airport.id,
    iata_code: airport.iata_code,
    name: airport.name,
    city_name: airport.city_name,
    country_code: airport.country_code,
    latitude: airport.latitude,
    longitude: airport.longitude,
    updated_at: new Date().toISOString(),
  };
}

async function upsertAirports(supabase: any, mapped: any[]) {
  const chunkSize = 500;
  for (let i = 0; i < mapped.length; i += chunkSize) {
    const chunk = mapped.slice(i, i + chunkSize);
    const { error } = await supabase.from('airports').upsert(chunk, { onConflict: 'iata_code' });
    if (error) {
      throw new Error(error.message);
    }
  }
}

export async function POST() {
  try {
    const supabase = createClient(
      process.env.SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
    const DUFFEL_TOKEN = process.env.DUFFEL_TOKEN!;
    
    console.log('Starting manual airport sync...');
    const duffelAirports = await fetchAllDuffelAirports(DUFFEL_TOKEN);
    console.log(`Fetched ${duffelAirports.length} airports from Duffel`);
    
    const mapped = duffelAirports.map(mapDuffelToSupabase);
    await upsertAirports(supabase, mapped);
    
    console.log('Airport sync completed successfully');
    return NextResponse.json({ 
      success: true,
      message: 'Sync complete', 
      count: mapped.length 
    });
  } catch (err: any) {
    console.error('Error during sync:', err);
    return NextResponse.json(
      { 
        success: false,
        error: err.message || 'Internal Server Error' 
      },
      { status: 500 }
    );
  }
}
